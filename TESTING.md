# VoicePilot 測試指南

## 功能測試清單

### ✅ 基本功能測試

1. **應用程式啟動**
   ```bash
   npm start
   ```
   - [ ] 應用程式成功啟動
   - [ ] 系統托盤顯示VoicePilot圖示
   - [ ] 控制台顯示 "VoicePilot initialized successfully"

2. **全域快捷鍵測試**
   - [ ] 在任何應用程式中按 `Ctrl+Shift+V` 
   - [ ] 控制台顯示 "Starting voice recording..."
   - [ ] 再次按 `Ctrl+Shift+V`
   - [ ] 控制台顯示 "Stopping voice recording..."

3. **設定介面測試**
   - [ ] 右鍵點擊系統托盤圖示
   - [ ] 選擇 "Settings" 開啟設定視窗
   - [ ] 設定視窗正常顯示
   - [ ] 可以修改快捷鍵設定
   - [ ] 可以輸入Azure OpenAI設定

4. **自動貼上功能測試**
   - [ ] 開啟記事本或任何文字編輯器
   - [ ] 點擊文字輸入區域
   - [ ] 按 `Ctrl+Shift+V` 開始錄音
   - [ ] 再次按 `Ctrl+Shift+V` 停止錄音
   - [ ] 文字自動貼上到編輯器中

### 🔧 Azure OpenAI 整合測試

**前提條件**: 需要有效的Azure OpenAI資源和API金鑰

1. **API連接測試**
   - [ ] 在設定中填入正確的Endpoint和API Key
   - [ ] 點擊 "測試連接" 按鈕
   - [ ] 顯示 "API連接測試成功！"

2. **語音轉文字測試**
   - [ ] 配置好Azure OpenAI設定
   - [ ] 在文字編輯器中按 `Ctrl+Shift+V`
   - [ ] 說話錄音（目前為模擬版本）
   - [ ] 再次按 `Ctrl+Shift+V` 停止
   - [ ] AI回應自動貼上

### 📱 跨應用程式相容性測試

測試VoicePilot在不同應用程式中的表現：

- [ ] **記事本 (Notepad)** - 基本文字編輯
- [ ] **Microsoft Word** - 文件編輯
- [ ] **瀏覽器** - 網頁表單輸入
- [ ] **Outlook** - 郵件撰寫
- [ ] **Slack/Teams** - 即時通訊
- [ ] **Visual Studio Code** - 程式碼編輯器

### 🚨 錯誤處理測試

1. **無效設定測試**
   - [ ] 輸入錯誤的API Endpoint
   - [ ] 輸入錯誤的API Key
   - [ ] 確認顯示適當的錯誤訊息

2. **網路連接測試**
   - [ ] 斷開網路連接
   - [ ] 嘗試使用語音功能
   - [ ] 確認顯示網路錯誤訊息

3. **權限測試**
   - [ ] 確認麥克風權限已授予
   - [ ] 測試在沒有麥克風權限時的行為

## 效能測試

### 記憶體使用
- [ ] 啟動後記憶體使用量 < 100MB
- [ ] 長時間運行後無記憶體洩漏

### CPU使用
- [ ] 待機時CPU使用量 < 1%
- [ ] 錄音時CPU使用量合理

### 回應時間
- [ ] 快捷鍵回應時間 < 100ms
- [ ] 設定視窗開啟時間 < 500ms

## 已知限制

1. **語音錄製**: 目前為模擬版本，需要實際的音訊錄製實現
2. **文字輸入**: 使用系統剪貼簿和自動貼上，某些應用程式可能有安全限制
3. **圖示**: 系統托盤使用空白圖示，需要設計實際圖示

## 故障排除

### 常見問題

1. **快捷鍵不工作**
   - 檢查是否有其他應用程式使用相同快捷鍵
   - 嘗試更換快捷鍵組合

2. **自動貼上失敗**
   - 確認目標應用程式允許自動化輸入
   - 檢查Windows安全設定

3. **設定無法儲存**
   - 檢查應用程式權限
   - 確認設定檔案可寫入

### 除錯模式

啟動除錯模式查看詳細日誌：
```bash
npm run dev
```

## 測試環境

- **作業系統**: Windows 10/11
- **Node.js**: 16+
- **Electron**: 28+
- **必要權限**: 麥克風、系統自動化

## 回報問題

如果發現問題，請提供：
1. 作業系統版本
2. 錯誤訊息截圖
3. 控制台日誌
4. 重現步驟
