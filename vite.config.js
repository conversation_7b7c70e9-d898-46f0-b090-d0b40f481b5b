import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  base: './',
  build: {
    outDir: 'dist-renderer',
    rollupOptions: {
      input: {
        settings: resolve(__dirname, 'src/renderer/settings.html'),
        'recording-status': resolve(__dirname, 'src/renderer/recording-status.html')
      }
    }
  },
  server: {
    port: 3000
  }
})
