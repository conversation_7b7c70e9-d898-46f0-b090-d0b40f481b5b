const { spawn } = require('child_process');
const os = require('os');

class TextInputService {
  constructor() {
    this.platform = os.platform();

    // 嘗試載入Electron clipboard，如果失敗則使用系統剪貼簿
    try {
      const { clipboard } = require('electron');
      this.clipboard = clipboard;
    } catch (error) {
      this.clipboard = null;
      console.log('Running outside Electron environment, using system clipboard');
    }
  }

  async typeText(text) {
    try {
      if (!text || typeof text !== 'string') {
        throw new Error('Invalid text input');
      }

      console.log('Auto-pasting text:', text.substring(0, 50) + '...');

      // 將文字複製到剪貼簿
      await this.setClipboard(text);

      // 等待一小段時間確保剪貼簿已更新
      await this.delay(100);

      // 自動執行貼上操作
      await this.simulatePaste();

      console.log('Text auto-pasted successfully');

    } catch (error) {
      console.error('Error auto-pasting text:', error);
      throw error;
    }
  }

  splitTextIntoChunks(text, chunkSize) {
    const chunks = [];
    for (let i = 0; i < text.length; i += chunkSize) {
      chunks.push(text.substring(i, i + chunkSize));
    }
    return chunks;
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async setClipboard(text) {
    if (this.clipboard) {
      // 在Electron環境中使用Electron clipboard
      this.clipboard.writeText(text);
    } else {
      // 在非Electron環境中使用系統剪貼簿
      await this.setSystemClipboard(text);
    }
  }

  async setSystemClipboard(text) {
    return new Promise((resolve, reject) => {
      if (this.platform === 'win32') {
        // Windows: 使用PowerShell設定剪貼簿
        const script = `Set-Clipboard -Value "${text.replace(/"/g, '""')}"`;
        const powershell = spawn('powershell', ['-Command', script], {
          windowsHide: true
        });

        powershell.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error(`PowerShell exited with code ${code}`));
          }
        });

        powershell.on('error', (error) => {
          reject(error);
        });

      } else if (this.platform === 'darwin') {
        // macOS: 使用pbcopy
        const pbcopy = spawn('pbcopy');
        pbcopy.stdin.write(text);
        pbcopy.stdin.end();

        pbcopy.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error(`pbcopy exited with code ${code}`));
          }
        });

        pbcopy.on('error', (error) => {
          reject(error);
        });

      } else {
        // Linux: 使用xclip
        const xclip = spawn('xclip', ['-selection', 'clipboard']);
        xclip.stdin.write(text);
        xclip.stdin.end();

        xclip.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error(`xclip exited with code ${code}`));
          }
        });

        xclip.on('error', (error) => {
          reject(error);
        });
      }
    });
  }

  async simulatePaste() {
    try {
      if (this.platform === 'win32') {
        // Windows: 使用PowerShell發送Ctrl+V
        await this.sendKeysWindows('ctrl+v');
      } else if (this.platform === 'darwin') {
        // macOS: 使用AppleScript發送Cmd+V
        await this.sendKeysMac('cmd+v');
      } else {
        // Linux: 使用xdotool發送Ctrl+V
        await this.sendKeysLinux('ctrl+v');
      }
    } catch (error) {
      console.error('Error simulating paste:', error);
      // 降級：顯示通知讓用戶手動貼上
      this.showNotification('Text ready! Press Ctrl+V to paste.');
    }
  }

  async sendKeysWindows(keys) {
    return new Promise((resolve, reject) => {
      // 使用PowerShell的SendKeys功能
      const script = `
        Add-Type -AssemblyName System.Windows.Forms
        [System.Windows.Forms.SendKeys]::SendWait("^v")
      `;

      const powershell = spawn('powershell', ['-Command', script], {
        windowsHide: true
      });

      powershell.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`PowerShell exited with code ${code}`));
        }
      });

      powershell.on('error', (error) => {
        reject(error);
      });
    });
  }

  async sendKeysMac(keys) {
    return new Promise((resolve, reject) => {
      // 使用AppleScript發送按鍵
      const script = `tell application "System Events" to keystroke "v" using command down`;

      const osascript = spawn('osascript', ['-e', script]);

      osascript.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`osascript exited with code ${code}`));
        }
      });

      osascript.on('error', (error) => {
        reject(error);
      });
    });
  }

  async sendKeysLinux(keys) {
    return new Promise((resolve, reject) => {
      // 使用xdotool發送按鍵
      const xdotool = spawn('xdotool', ['key', 'ctrl+v']);

      xdotool.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`xdotool exited with code ${code}`));
        }
      });

      xdotool.on('error', (error) => {
        reject(error);
      });
    });
  }

  // 顯示系統通知
  showNotification(message) {
    try {
      const { Notification } = require('electron');

      if (Notification.isSupported()) {
        new Notification({
          title: 'VoicePilot',
          body: message,
          silent: false
        }).show();
      }
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  }

  // 簡化版本：直接使用剪貼簿
  async insertText(text) {
    return this.typeText(text);
  }

  async replaceSelectedText(text) {
    return this.typeText(text);
  }

  async smartTypeText(text) {
    return this.typeText(text);
  }
}

module.exports = TextInputService;
