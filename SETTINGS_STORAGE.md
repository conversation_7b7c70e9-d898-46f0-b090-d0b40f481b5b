# SpeechPilot 設定儲存說明

## 設定儲存位置

SpeechPilot 使用 `electron-store` 來儲存用戶設定。設定檔案會自動儲存在系統的標準應用程式資料目錄中：

### Windows
```
%APPDATA%\SpeechPilot\config.json
```
完整路徑範例：
```
C:\Users\<USER>\AppData\Roaming\SpeechPilot\config.json
```

### macOS
```
~/Library/Application Support/SpeechPilot/config.json
```
完整路徑範例：
```
/Users/<USER>/Library/Application Support/SpeechPilot/config.json
```

### Linux
```
~/.config/SpeechPilot/config.json
```
完整路徑範例：
```
/home/<USER>/.config/SpeechPilot/config.json
```

## 設定檔案內容

設定檔案是一個 JSON 格式的檔案，包含以下內容：

```json
{
  "hotkey": "CommandOrControl+Shift+V",
  "azureEndpoint": "https://your-resource.openai.azure.com",
  "azureApiKey": "your-api-key-here",
  "model": "gpt-4o-mini-audio-preview"
}
```

## 設定優先順序

SpeechPilot 支援多種設定方式，優先順序如下（由高到低）：

1. **UI 設定** - 透過設定視窗儲存的值（儲存在 config.json）
2. **環境變數** - 從 .env 檔案或系統環境變數讀取
3. **預設值** - 程式內建的預設值

### 環境變數支援

你可以使用以下環境變數：

- `AZURE_OPENAI_ENDPOINT` - Azure OpenAI 端點
- `AZURE_OPENAI_API_KEY` - Azure OpenAI API 金鑰
- `AZURE_OPENAI_MODEL` - AI 模型名稱

### 使用 .env 檔案

1. 複製 `.env.example` 為 `.env`
2. 填入你的實際設定值
3. 重新啟動應用程式

```bash
cp .env.example .env
# 編輯 .env 檔案
```

## 設定管理

### 查看設定檔案位置
你可以透過以下方式找到設定檔案：

1. **程式碼方式**：
```javascript
const Store = require('electron-store');
const store = new Store();
console.log(store.path);
```

2. **手動查找**：
   - Windows: 按 `Win + R`，輸入 `%APPDATA%\SpeechPilot`
   - macOS: 在 Finder 中按 `Cmd + Shift + G`，輸入 `~/Library/Application Support/SpeechPilot`
   - Linux: 在檔案管理器中導航到 `~/.config/SpeechPilot`

### 重置設定
如果需要重置所有設定：

1. 關閉 SpeechPilot 應用程式
2. 刪除設定檔案 `config.json`
3. 重新啟動應用程式

### 備份設定
建議定期備份設定檔案，特別是在重要的 API 金鑰設定完成後。

### 安全注意事項

- 設定檔案包含敏感的 API 金鑰資訊
- 請勿將包含真實 API 金鑰的設定檔案分享給他人
- 建議使用環境變數來管理敏感資訊，特別是在開發環境中

## 故障排除

### 設定無法儲存
1. 檢查應用程式是否有寫入權限
2. 確認磁碟空間充足
3. 檢查防毒軟體是否阻擋檔案寫入

### 設定遺失
1. 檢查設定檔案是否存在
2. 確認檔案格式是否正確的 JSON
3. 如果檔案損壞，刪除後重新設定

### 環境變數不生效
1. 確認 .env 檔案位於專案根目錄
2. 檢查環境變數名稱是否正確
3. 重新啟動應用程式
