import{r,j as e,c as w}from"./client-B2hCCc6G.js";const{ipcRenderer:l}=window.require("electron"),j=()=>{const[o,d]=r.useState({hotkey:"CommandOrControl+Shift+V",azureEndpoint:"",azureApiKey:"",model:"gpt-4o-mini-audio-preview"}),[c,u]=r.useState({message:"",type:""}),[p,m]=r.useState(!1);r.useEffect(()=>{h()},[]);const h=async()=>{try{const n=await l.invoke("get-settings");d(n)}catch(n){s("載入設定失敗: "+n.message,"error")}},i=n=>{const{name:t,value:a}=n.target;d(A=>({...A,[t]:a}))},s=(n,t)=>{u({message:n,type:t}),(t==="error"||t==="success")&&setTimeout(()=>{u({message:"",type:""})},5e3)},y=n=>{try{const t=new URL(n);return t.protocol==="http:"||t.protocol==="https:"}catch{return!1}},x=async n=>{n.preventDefault();try{s("正在儲存設定...","info");const t={hotkey:o.hotkey,azureEndpoint:o.azureEndpoint.trim(),azureApiKey:o.azureApiKey.trim(),model:o.model};if(!t.azureEndpoint)throw new Error("請輸入Azure OpenAI Endpoint");if(!t.azureApiKey)throw new Error("請輸入Azure API Key");if(!y(t.azureEndpoint))throw new Error("請輸入有效的Endpoint URL");const a=await l.invoke("save-settings",t);if(a.success)s("設定已成功儲存！","success"),setTimeout(()=>{window.close()},2e3);else throw new Error(a.error||"儲存失敗")}catch(t){s("儲存設定失敗: "+t.message,"error")}},g=async()=>{try{m(!0),s("正在測試API連接...","info");const n={azureEndpoint:o.azureEndpoint.trim(),azureApiKey:o.azureApiKey.trim(),model:o.model};if(!n.azureEndpoint||!n.azureApiKey)throw new Error("請先填入Endpoint和API Key");await l.invoke("save-settings",n);const t=await l.invoke("test-api-connection");if(t.success)s("API連接測試成功！","success");else throw new Error(t.error||"連接失敗")}catch(n){s("連接測試失敗: "+n.message,"error")}finally{m(!1)}},v=()=>{window.close()};return r.useEffect(()=>{const n=t=>{t.key==="Escape"&&window.close()};return document.addEventListener("keydown",n),()=>document.removeEventListener("keydown",n)},[]),e.jsxs("div",{className:"container",children:[e.jsx("h1",{children:"SpeechPilot 設定"}),e.jsxs("form",{onSubmit:x,children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"hotkey",children:"全域快捷鍵:"}),e.jsxs("select",{id:"hotkey",name:"hotkey",value:o.hotkey,onChange:i,children:[e.jsx("option",{value:"CommandOrControl+Shift+V",children:"Ctrl+Shift+V"}),e.jsx("option",{value:"CommandOrControl+Shift+A",children:"Ctrl+Shift+A"}),e.jsx("option",{value:"CommandOrControl+Alt+V",children:"Ctrl+Alt+V"}),e.jsx("option",{value:"F12",children:"F12"})]}),e.jsx("div",{className:"help-text",children:"選擇啟動語音錄製的快捷鍵"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"azureEndpoint",children:"Azure OpenAI Endpoint:"}),e.jsx("input",{type:"text",id:"azureEndpoint",name:"azureEndpoint",value:o.azureEndpoint,onChange:i,placeholder:"https://your-resource.openai.azure.com"}),e.jsx("div",{className:"help-text",children:"你的Azure OpenAI資源端點URL"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"azureApiKey",children:"Azure API Key:"}),e.jsx("input",{type:"password",id:"azureApiKey",name:"azureApiKey",value:o.azureApiKey,onChange:i,placeholder:"輸入你的API金鑰"}),e.jsx("div",{className:"help-text",children:"Azure OpenAI的API金鑰"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"model",children:"AI模型:"}),e.jsx("input",{type:"text",id:"model",name:"model",value:o.model,onChange:i,placeholder:"gpt-4o-mini-audio-preview"}),e.jsx("div",{className:"help-text",children:"輸入要使用的AI模型名稱（建議使用 gpt-4o-mini-audio-preview）"})]}),e.jsxs("div",{className:"button-group",children:[e.jsx("button",{type:"button",onClick:g,disabled:p,className:"btn-test",children:p?"測試中...":"測試連接"}),e.jsx("button",{type:"submit",className:"btn-primary",children:"儲存設定"}),e.jsx("button",{type:"button",onClick:v,className:"btn-secondary",children:"取消"})]})]}),c.message&&e.jsx("div",{className:`status ${c.type}`,children:c.message})]})},E=document.getElementById("root"),f=w.createRoot(E);f.render(e.jsx(j,{}));
