body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    overflow: hidden;
}

.recording-container {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 300px;
}

.recording-icon {
    width: 80px;
    height: 80px;
    background: #ff4757;
    border-radius: 50%;
    margin: 0 auto 20px;
    position: relative;
    animation: pulse 1.5s ease-in-out infinite;
}

.recording-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 40px;
    background: white;
    border-radius: 15px 15px 0 0;
}

.recording-icon::after {
    content: '';
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 15px;
    background: white;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 20px rgba(255, 71, 87, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
    }
}

.recording-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.recording-subtitle {
    font-size: 16px;
    color: #7f8c8d;
    margin-bottom: 20px;
}

.recording-status {
    font-size: 14px;
    color: #34495e;
    margin-bottom: 20px;
    padding: 10px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.wave-animation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3px;
    margin: 20px 0;
}

.wave-bar {
    width: 4px;
    height: 20px;
    background: #3498db;
    border-radius: 2px;
    animation: wave 1.2s ease-in-out infinite;
}

.wave-bar:nth-child(2) { animation-delay: 0.1s; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; }
.wave-bar:nth-child(6) { animation-delay: 0.5s; }
.wave-bar:nth-child(7) { animation-delay: 0.6s; }

@keyframes wave {
    0%, 40%, 100% {
        transform: scaleY(0.4);
    }
    20% {
        transform: scaleY(1);
    }
}

.tips {
    font-size: 12px;
    color: #95a5a6;
    margin-top: 20px;
    line-height: 1.4;
}

.processing .recording-icon {
    background: #f39c12;
    animation: spin 2s linear infinite;
}

.processing .recording-icon::before {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    top: 30%;
    left: 30%;
}

.processing .recording-icon::after {
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error .recording-icon {
    background: #e74c3c;
    animation: shake 0.5s ease-in-out;
}

.error .recording-icon::before {
    content: '✕';
    width: auto;
    height: auto;
    background: none;
    color: white;
    font-size: 30px;
    font-weight: bold;
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.error .recording-icon::after {
    display: none;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
