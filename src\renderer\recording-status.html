<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoicePilot - 錄音中</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
        }
        
        .recording-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 300px;
        }
        
        .recording-icon {
            width: 80px;
            height: 80px;
            background: #ff4757;
            border-radius: 50%;
            margin: 0 auto 20px;
            position: relative;
            animation: pulse 1.5s ease-in-out infinite;
        }
        
        .recording-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 40px;
            background: white;
            border-radius: 15px 15px 0 0;
        }
        
        .recording-icon::after {
            content: '';
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 15px;
            background: white;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
            }
            70% {
                transform: scale(1.1);
                box-shadow: 0 0 0 20px rgba(255, 71, 87, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
            }
        }
        
        .recording-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .recording-subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        
        .recording-status {
            font-size: 14px;
            color: #34495e;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .wave-animation {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 3px;
            margin: 20px 0;
        }
        
        .wave-bar {
            width: 4px;
            height: 20px;
            background: #3498db;
            border-radius: 2px;
            animation: wave 1.2s ease-in-out infinite;
        }
        
        .wave-bar:nth-child(2) { animation-delay: 0.1s; }
        .wave-bar:nth-child(3) { animation-delay: 0.2s; }
        .wave-bar:nth-child(4) { animation-delay: 0.3s; }
        .wave-bar:nth-child(5) { animation-delay: 0.4s; }
        .wave-bar:nth-child(6) { animation-delay: 0.5s; }
        .wave-bar:nth-child(7) { animation-delay: 0.6s; }
        
        @keyframes wave {
            0%, 40%, 100% {
                transform: scaleY(0.4);
            }
            20% {
                transform: scaleY(1);
            }
        }
        
        .tips {
            font-size: 12px;
            color: #95a5a6;
            margin-top: 20px;
            line-height: 1.4;
        }
        
        .processing {
            display: none;
        }
        
        .processing .recording-icon {
            background: #f39c12;
            animation: spin 2s linear infinite;
        }
        
        .processing .recording-icon::before {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            top: 30%;
            left: 30%;
        }
        
        .processing .recording-icon::after {
            display: none;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            display: none;
        }
        
        .error .recording-icon {
            background: #e74c3c;
            animation: shake 0.5s ease-in-out;
        }
        
        .error .recording-icon::before {
            content: '✕';
            width: auto;
            height: auto;
            background: none;
            color: white;
            font-size: 30px;
            font-weight: bold;
            border-radius: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error .recording-icon::after {
            display: none;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div class="recording-container">
        <div class="recording-state" id="recordingState">
            <div class="recording-icon"></div>
            <div class="recording-title">正在錄音</div>
            <div class="recording-subtitle">請開始說話...</div>
            <div class="wave-animation">
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
            </div>
            <div class="recording-status">
                再次按下快捷鍵停止錄音
            </div>
            <div class="tips">
                💡 說話清晰一些，AI 會更好理解您的指令<br>
                🎯 您可以說「寫一封郵件」、「翻譯成英文」等指令
            </div>
        </div>
        
        <div class="processing-state processing" id="processingState">
            <div class="recording-icon"></div>
            <div class="recording-title">處理中</div>
            <div class="recording-subtitle">AI 正在理解您的指令...</div>
            <div class="recording-status">
                請稍候，這可能需要幾秒鐘
            </div>
        </div>
        
        <div class="error-state error" id="errorState">
            <div class="recording-icon"></div>
            <div class="recording-title">發生錯誤</div>
            <div class="recording-subtitle" id="errorMessage">處理失敗</div>
            <div class="recording-status">
                視窗將在 3 秒後自動關閉
            </div>
        </div>
    </div>

    <script src="recording-status.js"></script>
</body>
</html>
