import{r as c,j as e,c as j}from"./client-B2hCCc6G.js";const{ipcRenderer:i}=window.require("electron"),h=()=>{const[o,t]=c.useState("recording"),[d,l]=c.useState("");c.useEffect(()=>{const r=(a,x)=>{m(x)};i.on("recording-status-update",r);const s=a=>{a.key==="Escape"&&window.close()};return document.addEventListener("keydown",s),()=>{i.removeListener("recording-status-update",r),document.removeEventListener("keydown",s)}},[]);const m=r=>{switch(r.type){case"recording":t("recording");break;case"processing":t("processing");break;case"error":t("error"),l(r.message||"處理失敗"),setTimeout(()=>{window.close()},3e3);break;case"complete":window.close();break}},n=()=>e.jsxs("div",{className:"recording-state",children:[e.jsx("div",{className:"recording-icon"}),e.jsx("div",{className:"recording-title",children:"正在錄音"}),e.jsx("div",{className:"recording-subtitle",children:"請開始說話..."}),e.jsx("div",{className:"wave-animation",children:[...Array(7)].map((r,s)=>e.jsx("div",{className:"wave-bar"},s))}),e.jsx("div",{className:"recording-status",children:"再次按下快捷鍵停止錄音"}),e.jsxs("div",{className:"tips",children:["💡 說話清晰一些，AI 會更好理解您的指令",e.jsx("br",{}),"🎯 您可以說「寫一封郵件」、「翻譯成英文」等指令"]})]}),g=()=>e.jsxs("div",{className:"processing-state processing",children:[e.jsx("div",{className:"recording-icon"}),e.jsx("div",{className:"recording-title",children:"處理中"}),e.jsx("div",{className:"recording-subtitle",children:"AI 正在理解您的指令..."}),e.jsx("div",{className:"recording-status",children:"請稍候，這可能需要幾秒鐘"})]}),u=()=>e.jsxs("div",{className:"error-state error",children:[e.jsx("div",{className:"recording-icon"}),e.jsx("div",{className:"recording-title",children:"發生錯誤"}),e.jsx("div",{className:"recording-subtitle",children:d}),e.jsx("div",{className:"recording-status",children:"視窗將在 3 秒後自動關閉"})]}),v=()=>{switch(o){case"recording":return n();case"processing":return g();case"error":return u();default:return n()}};return e.jsx("div",{className:"recording-container",children:v()})},p=document.getElementById("root"),N=j.createRoot(p);N.render(e.jsx(h,{}));
