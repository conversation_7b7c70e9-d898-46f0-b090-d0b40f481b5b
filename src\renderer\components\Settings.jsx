import React, { useState, useEffect } from 'react';
import './Settings.css';

const { ipc<PERSON><PERSON><PERSON> } = window.require('electron');

const Settings = () => {
  const [settings, setSettings] = useState({
    hotkey: 'CommandOrControl+Shift+V',
    azureEndpoint: '',
    azureApiKey: '',
    model: 'gpt-4o-mini-audio-preview'
  });
  
  const [status, setStatus] = useState({ message: '', type: '' });
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const loadedSettings = await ipcRenderer.invoke('get-settings');
      setSettings(loadedSettings);
    } catch (error) {
      showStatus('載入設定失敗: ' + error.message, 'error');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const showStatus = (message, type) => {
    setStatus({ message, type });
    
    if (type === 'error' || type === 'success') {
      setTimeout(() => {
        setStatus({ message: '', type: '' });
      }, 5000);
    }
  };

  const isValidUrl = (string) => {
    try {
      const url = new URL(string);
      return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (_) {
      return false;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      showStatus('正在儲存設定...', 'info');
      
      const settingsToSave = {
        hotkey: settings.hotkey,
        azureEndpoint: settings.azureEndpoint.trim(),
        azureApiKey: settings.azureApiKey.trim(),
        model: settings.model
      };
      
      // 驗證必填欄位
      if (!settingsToSave.azureEndpoint) {
        throw new Error('請輸入Azure OpenAI Endpoint');
      }
      
      if (!settingsToSave.azureApiKey) {
        throw new Error('請輸入Azure API Key');
      }
      
      // 驗證endpoint格式
      if (!isValidUrl(settingsToSave.azureEndpoint)) {
        throw new Error('請輸入有效的Endpoint URL');
      }
      
      const result = await ipcRenderer.invoke('save-settings', settingsToSave);
      
      if (result.success) {
        showStatus('設定已成功儲存！', 'success');
        
        // 2秒後關閉視窗
        setTimeout(() => {
          window.close();
        }, 2000);
      } else {
        throw new Error(result.error || '儲存失敗');
      }
      
    } catch (error) {
      showStatus('儲存設定失敗: ' + error.message, 'error');
    }
  };

  const testConnection = async () => {
    try {
      setIsTestingConnection(true);
      showStatus('正在測試API連接...', 'info');
      
      // 先儲存當前設定以便測試
      const testSettings = {
        azureEndpoint: settings.azureEndpoint.trim(),
        azureApiKey: settings.azureApiKey.trim(),
        model: settings.model
      };
      
      if (!testSettings.azureEndpoint || !testSettings.azureApiKey) {
        throw new Error('請先填入Endpoint和API Key');
      }
      
      // 暫時儲存設定用於測試
      await ipcRenderer.invoke('save-settings', testSettings);
      
      // 測試連接
      const testResult = await ipcRenderer.invoke('test-api-connection');
      
      if (testResult.success) {
        showStatus('API連接測試成功！', 'success');
      } else {
        throw new Error(testResult.error || '連接失敗');
      }
      
    } catch (error) {
      showStatus('連接測試失敗: ' + error.message, 'error');
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleCancel = () => {
    window.close();
  };

  // 處理ESC鍵關閉視窗
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        window.close();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <div className="container">
      <h1>SpeechPilot 設定</h1>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="hotkey">全域快捷鍵:</label>
          <select 
            id="hotkey" 
            name="hotkey" 
            value={settings.hotkey}
            onChange={handleInputChange}
          >
            <option value="CommandOrControl+Shift+V">Ctrl+Shift+V</option>
            <option value="CommandOrControl+Shift+A">Ctrl+Shift+A</option>
            <option value="CommandOrControl+Alt+V">Ctrl+Alt+V</option>
            <option value="F12">F12</option>
          </select>
          <div className="help-text">選擇啟動語音錄製的快捷鍵</div>
        </div>
        
        <div className="form-group">
          <label htmlFor="azureEndpoint">Azure OpenAI Endpoint:</label>
          <input 
            type="text" 
            id="azureEndpoint" 
            name="azureEndpoint"
            value={settings.azureEndpoint}
            onChange={handleInputChange}
            placeholder="https://your-resource.openai.azure.com"
          />
          <div className="help-text">你的Azure OpenAI資源端點URL</div>
        </div>
        
        <div className="form-group">
          <label htmlFor="azureApiKey">Azure API Key:</label>
          <input 
            type="password" 
            id="azureApiKey" 
            name="azureApiKey"
            value={settings.azureApiKey}
            onChange={handleInputChange}
            placeholder="輸入你的API金鑰"
          />
          <div className="help-text">Azure OpenAI的API金鑰</div>
        </div>
        
        <div className="form-group">
          <label htmlFor="model">AI模型:</label>
          <input 
            type="text" 
            id="model" 
            name="model"
            value={settings.model}
            onChange={handleInputChange}
            placeholder="gpt-4o-mini-audio-preview"
          />
          <div className="help-text">輸入要使用的AI模型名稱（建議使用 gpt-4o-mini-audio-preview）</div>
        </div>
        
        <div className="button-group">
          <button 
            type="button" 
            onClick={testConnection}
            disabled={isTestingConnection}
            className="btn-test"
          >
            {isTestingConnection ? '測試中...' : '測試連接'}
          </button>
          <button type="submit" className="btn-primary">儲存設定</button>
          <button type="button" onClick={handleCancel} className="btn-secondary">取消</button>
        </div>
      </form>
      
      {status.message && (
        <div className={`status ${status.type}`}>
          {status.message}
        </div>
      )}
    </div>
  );
};

export default Settings;
