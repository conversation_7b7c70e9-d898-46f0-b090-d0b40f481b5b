{"name": "speech-pilot", "version": "1.0.0", "description": "AI-powered speech assistant for global text input", "main": "src/main.js", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "wait-on http://localhost:3000 && cross-env NODE_ENV=development electron .", "build": "npm run build:renderer && electron-builder", "build:renderer": "vite build", "build:win": "npm run build:renderer && electron-builder --win", "build:mac": "npm run build:renderer && electron-builder --mac", "start": "electron .", "test": "jest"}, "keywords": ["electron", "voice", "ai", "automation", "speech-to-text"], "author": "Your Name", "license": "MIT", "devDependencies": {"@vitejs/plugin-react": "^4.7.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^28.0.0", "electron-builder": "^24.0.0", "jest": "^29.0.0", "vite": "^7.0.5", "wait-on": "^8.0.3"}, "dependencies": {"axios": "^1.6.0", "dotenv": "^17.2.0", "electron-store": "^8.1.0", "form-data": "^4.0.0", "node-record-lpcm16": "^1.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "robotjs": "^0.6.0"}, "build": {"appId": "com.speechpilot.app", "productName": "SpeechPilot", "directories": {"output": "dist"}, "files": ["src/main.js", "src/services/**/*", "dist-renderer/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}}}