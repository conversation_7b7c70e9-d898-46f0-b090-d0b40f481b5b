require('dotenv').config();
const { app, BrowserWindow, globalShortcut, ipcMain, Tray, Menu, nativeImage } = require('electron');
const path = require('path');
const Store = require('electron-store');
const VoiceRecorder = require('./services/VoiceRecorder');
const AIService = require('./services/AIService');
const TextInputService = require('./services/TextInputService');

// 初始化設定存儲
const store = new Store();

// 判斷是否為開發模式
const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
const VITE_DEV_SERVER_URL = 'http://localhost:3000';

class SpeechPilotApp {
  constructor() {
    this.mainWindow = null;
    this.recordingWindow = null;
    this.tray = null;
    this.voiceRecorder = new VoiceRecorder();
    this.aiService = new AIService();
    this.textInputService = new TextInputService();
    this.isRecording = false;
  }

  async initialize() {
    await app.whenReady();
    
    // 建立系統托盤
    this.createTray();
    
    // 註冊全域快捷鍵
    this.registerGlobalShortcuts();
    
    // 設定IPC處理器
    this.setupIpcHandlers();
    
    console.log('SpeechPilot initialized successfully');
  }

  createTray() {
    // 建立托盤圖示 (使用空圖示，因為我們還沒有實際圖示檔案)
    const icon = nativeImage.createEmpty();
    this.tray = new Tray(icon);
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'SpeechPilot',
        type: 'normal',
        enabled: false
      },
      { type: 'separator' },
      {
        label: 'Status: Ready',
        type: 'normal',
        enabled: false
      },
      { type: 'separator' },
      {
        label: 'Settings',
        type: 'normal',
        click: () => this.showSettings()
      },
      {
        label: 'Quit',
        type: 'normal',
        click: () => app.quit()
      }
    ]);
    
    this.tray.setContextMenu(contextMenu);
    this.tray.setToolTip('SpeechPilot - AI Speech Assistant');
  }

  registerGlobalShortcuts() {
    // 註冊 Ctrl+Shift+V 作為啟動快捷鍵
    const shortcut = store.get('hotkey', 'CommandOrControl+Shift+V');
    
    const success = globalShortcut.register(shortcut, () => {
      this.toggleRecording();
    });

    if (!success) {
      console.error('Failed to register global shortcut');
    } else {
      console.log(`Global shortcut registered: ${shortcut}`);
    }
  }

  async toggleRecording() {
    if (this.isRecording) {
      await this.stopRecording();
    } else {
      await this.startRecording();
    }
  }

  async startRecording() {
    try {
      this.isRecording = true;
      this.updateTrayStatus('Recording...');

      // 顯示錄音狀態視窗
      this.showRecordingWindow();

      console.log('Starting voice recording...');
      await this.voiceRecorder.startRecording();

    } catch (error) {
      console.error('Failed to start recording:', error);
      this.isRecording = false;
      this.updateTrayStatus('Error');
      this.updateRecordingStatus({ type: 'error', message: '錄音啟動失敗' });
    }
  }

  async stopRecording() {
    try {
      this.updateTrayStatus('Processing...');
      this.updateRecordingStatus({ type: 'processing' });

      console.log('Stopping voice recording...');
      const audioBuffer = await this.voiceRecorder.stopRecording();

      console.log('Sending audio to AI service...');
      const aiResponse = await this.aiService.processAudio(audioBuffer);

      console.log('AI Response:', aiResponse);

      // 將AI回應輸入到當前焦點的應用程式
      await this.textInputService.typeText(aiResponse);

      this.isRecording = false;
      this.updateTrayStatus('Ready');
      this.updateRecordingStatus({ type: 'complete' });

    } catch (error) {
      console.error('Failed to process recording:', error);
      this.isRecording = false;
      this.updateTrayStatus('Error');
      this.updateRecordingStatus({ type: 'error', message: error.message });
    }
  }

  updateTrayStatus(status) {
    if (this.tray) {
      const contextMenu = Menu.buildFromTemplate([
        {
          label: 'SpeechPilot',
          type: 'normal',
          enabled: false
        },
        { type: 'separator' },
        {
          label: `Status: ${status}`,
          type: 'normal',
          enabled: false
        },
        { type: 'separator' },
        {
          label: 'Settings',
          type: 'normal',
          click: () => this.showSettings()
        },
        {
          label: 'Quit',
          type: 'normal',
          click: () => app.quit()
        }
      ]);
      
      this.tray.setContextMenu(contextMenu);
    }
  }

  showSettings() {
    if (this.mainWindow) {
      this.mainWindow.focus();
      return;
    }

    this.mainWindow = new BrowserWindow({
      width: 600,
      height: 500,
      minWidth: 500,
      minHeight: 400,
      maxWidth: 1000,
      maxHeight: 800,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      },
      show: false,
      resizable: true
    });

    if (isDev) {
      this.mainWindow.loadURL(`${VITE_DEV_SERVER_URL}/src/renderer/settings.html`);
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../dist-renderer/settings.html'));
    }

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  showRecordingWindow() {
    if (this.recordingWindow) {
      this.recordingWindow.focus();
      return;
    }

    this.recordingWindow = new BrowserWindow({
      width: 400,
      height: 350,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      },
      show: false,
      resizable: false,
      frame: false,
      alwaysOnTop: true,
      transparent: true,
      skipTaskbar: true
    });

    if (isDev) {
      this.recordingWindow.loadURL(`${VITE_DEV_SERVER_URL}/src/renderer/recording-status.html`);
    } else {
      this.recordingWindow.loadFile(path.join(__dirname, '../dist-renderer/recording-status.html'));
    }

    this.recordingWindow.once('ready-to-show', () => {
      this.recordingWindow.show();
      this.recordingWindow.center();
    });

    this.recordingWindow.on('closed', () => {
      this.recordingWindow = null;
    });
  }

  updateRecordingStatus(status) {
    if (this.recordingWindow && this.recordingWindow.webContents) {
      this.recordingWindow.webContents.send('recording-status-update', status);
    }
  }

  setupIpcHandlers() {
    ipcMain.handle('get-settings', () => {
      return {
        hotkey: store.get('hotkey', 'CommandOrControl+Shift+V'),
        azureEndpoint: store.get('azureEndpoint', process.env.AZURE_OPENAI_ENDPOINT || ''),
        azureApiKey: store.get('azureApiKey', process.env.AZURE_OPENAI_API_KEY || ''),
        model: store.get('model', process.env.AZURE_OPENAI_MODEL || 'gpt-4o-mini-audio-preview')
      };
    });

    ipcMain.handle('save-settings', (event, settings) => {
      store.set('hotkey', settings.hotkey);
      store.set('azureEndpoint', settings.azureEndpoint);
      store.set('azureApiKey', settings.azureApiKey);
      store.set('model', settings.model);

      // 重新註冊快捷鍵
      globalShortcut.unregisterAll();
      this.registerGlobalShortcuts();

      return { success: true };
    });

    ipcMain.handle('test-api-connection', async () => {
      try {
        const result = await this.aiService.testConnection();
        return result;
      } catch (error) {
        return { success: false, error: error.message };
      }
    });
  }
}

// 應用程式事件處理
app.whenReady().then(async () => {
  const speechPilot = new SpeechPilotApp();
  await speechPilot.initialize();
});

app.on('window-all-closed', (event) => {
  // 防止應用程式完全關閉，保持在系統托盤
  event.preventDefault();
});

app.on('will-quit', () => {
  // 清理全域快捷鍵
  globalShortcut.unregisterAll();
});

app.on('activate', () => {
  // macOS 特定行為
  if (BrowserWindow.getAllWindows().length === 0) {
    // 不需要重新建立視窗，因為我們使用系統托盤
  }
});
