import React, { useState, useEffect } from 'react';
import './RecordingStatus.css';

const { ipc<PERSON>enderer } = window.require('electron');

const RecordingStatus = () => {
  const [currentState, setCurrentState] = useState('recording');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    // 監聽來自主程序的狀態更新
    const handleStatusUpdate = (event, status) => {
      updateStatus(status);
    };

    ipcRenderer.on('recording-status-update', handleStatusUpdate);

    // 處理ESC鍵關閉視窗
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        window.close();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      ipcRenderer.removeListener('recording-status-update', handleStatusUpdate);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const updateStatus = (status) => {
    switch (status.type) {
      case 'recording':
        setCurrentState('recording');
        break;
      case 'processing':
        setCurrentState('processing');
        break;
      case 'error':
        setCurrentState('error');
        setErrorMessage(status.message || '處理失敗');
        // 3秒後自動關閉
        setTimeout(() => {
          window.close();
        }, 3000);
        break;
      case 'complete':
        window.close();
        break;
      default:
        break;
    }
  };

  const renderRecordingState = () => (
    <div className="recording-state">
      <div className="recording-icon"></div>
      <div className="recording-title">正在錄音</div>
      <div className="recording-subtitle">請開始說話...</div>
      <div className="wave-animation">
        {[...Array(7)].map((_, i) => (
          <div key={i} className="wave-bar"></div>
        ))}
      </div>
      <div className="recording-status">
        再次按下快捷鍵停止錄音
      </div>
      <div className="tips">
        💡 說話清晰一些，AI 會更好理解您的指令<br />
        🎯 您可以說「寫一封郵件」、「翻譯成英文」等指令
      </div>
    </div>
  );

  const renderProcessingState = () => (
    <div className="processing-state processing">
      <div className="recording-icon"></div>
      <div className="recording-title">處理中</div>
      <div className="recording-subtitle">AI 正在理解您的指令...</div>
      <div className="recording-status">
        請稍候，這可能需要幾秒鐘
      </div>
    </div>
  );

  const renderErrorState = () => (
    <div className="error-state error">
      <div className="recording-icon"></div>
      <div className="recording-title">發生錯誤</div>
      <div className="recording-subtitle">{errorMessage}</div>
      <div className="recording-status">
        視窗將在 3 秒後自動關閉
      </div>
    </div>
  );

  const renderCurrentState = () => {
    switch (currentState) {
      case 'recording':
        return renderRecordingState();
      case 'processing':
        return renderProcessingState();
      case 'error':
        return renderErrorState();
      default:
        return renderRecordingState();
    }
  };

  return (
    <div className="recording-container">
      {renderCurrentState()}
    </div>
  );
};

export default RecordingStatus;
