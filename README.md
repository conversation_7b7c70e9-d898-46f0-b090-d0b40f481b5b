# SpeechPilot - AI語音助手

SpeechPilot是一個桌面應用程式，讓你可以在任何應用程式中使用語音輸入和AI指令處理。只需按下快捷鍵，說出你想要的內容，AI就會理解並自動輸入到當前的文字欄位中。

## 功能特色

- 🎤 **全域語音錄製**: 在任何應用程式中按下快捷鍵即可開始錄音
- 🤖 **AI指令理解**: 不只是語音轉文字，還能理解複雜指令如"寫一篇100字的文章"
- ⌨️ **自動文字輸入**: AI回應會自動輸入到當前焦點的應用程式
- 🌐 **跨平台支援**: 支援Windows和macOS
- 🔧 **簡單設定**: 透過系統托盤輕鬆配置

## 系統需求

- Windows 10+ 或 macOS 10.14+
- Node.js 16+
- Azure OpenAI API存取權限

## 安裝步驟

1. 克隆專案：
```bash
git clone <repository-url>
cd SpeechPilot
```

2. 安裝依賴：
```bash
npm install
```

3. 啟動應用程式：
```bash
npm start
```

## 設定

首次啟動時，請點擊系統托盤中的SpeechPilot圖示，選擇"Settings"進行設定：

1. **Azure OpenAI Endpoint**: 你的Azure OpenAI資源端點
2. **API Key**: Azure OpenAI的API金鑰
3. **AI模型**: 選擇要使用的模型（建議使用gpt-4o-mini）
4. **快捷鍵**: 設定啟動語音錄製的快捷鍵（預設：Ctrl+Shift+V）

## 使用方法

1. 確保SpeechPilot在背景運行（系統托盤會顯示圖示）
2. 在任何應用程式中點擊文字輸入欄位
3. 按下設定的快捷鍵（預設：Ctrl+Shift+V）
4. 開始說話，系統會自動錄音
5. 再次按下快捷鍵停止錄音
6. AI會處理你的語音並**自動貼上**結果到文字欄位

## 支援的指令類型

- **直接輸入**: "請輸入我的電子郵件地址"
- **寫作任務**: "寫一篇關於人工智慧的100字文章"
- **郵件撰寫**: "寫一封感謝信給客戶"
- **翻譯**: "把這段文字翻譯成英文"
- **格式化**: "把這些項目整理成列表"
- **編輯**: "修正這段文字的語法錯誤"

## 開發

### 專案結構
```
SpeechPilot/
├── src/
│   ├── main.js              # 主程序
│   ├── services/
│   │   ├── VoiceRecorder.js # 語音錄製服務
│   │   ├── AIService.js     # AI處理服務
│   │   └── TextInputService.js # 文字輸入服務
│   └── renderer/
│       ├── settings.html    # 設定介面
│       └── settings.js      # 設定邏輯
├── assets/                  # 圖示和資源
├── package.json
└── README.md
```

### 開發模式
```bash
npm run dev
```

### 建置應用程式
```bash
# Windows
npm run build:win

# macOS
npm run build:mac
```

## 故障排除

### 常見問題

1. **快捷鍵不工作**
   - 檢查是否有其他應用程式使用相同快捷鍵
   - 嘗試更換不同的快捷鍵組合

2. **語音錄製失敗**
   - 確保麥克風權限已開啟
   - 檢查系統音訊設定

3. **AI回應錯誤**
   - 驗證Azure OpenAI設定是否正確
   - 檢查API金鑰是否有效
   - 確認網路連接正常

4. **文字輸入不工作**
   - 確保目標應用程式的文字欄位已獲得焦點
   - 某些應用程式可能有安全限制

### 日誌查看

應用程式日誌會顯示在控制台中。開發模式下可以看到詳細的除錯資訊。

## 貢獻

歡迎提交Issue和Pull Request來改善這個專案。

## 授權

MIT License

## 更新日誌

### v1.0.0
- 初始版本
- 基本語音錄製和AI處理功能
- 系統托盤介面
- 設定管理
