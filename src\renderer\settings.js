const { ipcRenderer } = require('electron');

class SettingsManager {
    constructor() {
        this.form = document.getElementById('settingsForm');
        this.testBtn = document.getElementById('testBtn');
        this.cancelBtn = document.getElementById('cancelBtn');
        this.statusDiv = document.getElementById('status');
        
        this.init();
    }
    
    async init() {
        // 載入現有設定
        await this.loadSettings();
        
        // 綁定事件
        this.bindEvents();
    }
    
    async loadSettings() {
        try {
            const settings = await ipcRenderer.invoke('get-settings');
            
            document.getElementById('hotkey').value = settings.hotkey || 'CommandOrControl+Shift+V';
            document.getElementById('azureEndpoint').value = settings.azureEndpoint || '';
            document.getElementById('azureApiKey').value = settings.azureApiKey || '';
            document.getElementById('model').value = settings.model || 'gpt-4o-mini';
            
        } catch (error) {
            this.showStatus('載入設定失敗: ' + error.message, 'error');
        }
    }
    
    bindEvents() {
        // 表單提交
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSettings();
        });
        
        // 測試連接按鈕
        this.testBtn.addEventListener('click', () => {
            this.testConnection();
        });
        
        // 取消按鈕
        this.cancelBtn.addEventListener('click', () => {
            window.close();
        });
        
        // 按下ESC鍵關閉視窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                window.close();
            }
        });
    }
    
    async saveSettings() {
        try {
            this.showStatus('正在儲存設定...', 'info');
            
            const settings = {
                hotkey: document.getElementById('hotkey').value,
                azureEndpoint: document.getElementById('azureEndpoint').value.trim(),
                azureApiKey: document.getElementById('azureApiKey').value.trim(),
                model: document.getElementById('model').value
            };
            
            // 驗證必填欄位
            if (!settings.azureEndpoint) {
                throw new Error('請輸入Azure OpenAI Endpoint');
            }
            
            if (!settings.azureApiKey) {
                throw new Error('請輸入Azure API Key');
            }
            
            // 驗證endpoint格式
            if (!this.isValidUrl(settings.azureEndpoint)) {
                throw new Error('請輸入有效的Endpoint URL');
            }
            
            const result = await ipcRenderer.invoke('save-settings', settings);
            
            if (result.success) {
                this.showStatus('設定已成功儲存！', 'success');
                
                // 2秒後關閉視窗
                setTimeout(() => {
                    window.close();
                }, 2000);
            } else {
                throw new Error(result.error || '儲存失敗');
            }
            
        } catch (error) {
            this.showStatus('儲存設定失敗: ' + error.message, 'error');
        }
    }
    
    async testConnection() {
        try {
            this.testBtn.disabled = true;
            this.testBtn.textContent = '測試中...';
            this.showStatus('正在測試API連接...', 'info');
            
            // 先儲存當前設定以便測試
            const settings = {
                azureEndpoint: document.getElementById('azureEndpoint').value.trim(),
                azureApiKey: document.getElementById('azureApiKey').value.trim(),
                model: document.getElementById('model').value
            };
            
            if (!settings.azureEndpoint || !settings.azureApiKey) {
                throw new Error('請先填入Endpoint和API Key');
            }
            
            // 暫時儲存設定用於測試
            await ipcRenderer.invoke('save-settings', settings);
            
            // 測試連接 (這裡需要在主程序中實現測試功能)
            const testResult = await ipcRenderer.invoke('test-api-connection');
            
            if (testResult.success) {
                this.showStatus('API連接測試成功！', 'success');
            } else {
                throw new Error(testResult.error || '連接失敗');
            }
            
        } catch (error) {
            this.showStatus('連接測試失敗: ' + error.message, 'error');
        } finally {
            this.testBtn.disabled = false;
            this.testBtn.textContent = '測試連接';
        }
    }
    
    showStatus(message, type) {
        this.statusDiv.textContent = message;
        this.statusDiv.className = `status ${type}`;
        this.statusDiv.style.display = 'block';
        
        // 如果是錯誤或成功訊息，5秒後自動隱藏
        if (type === 'error' || type === 'success') {
            setTimeout(() => {
                this.statusDiv.style.display = 'none';
            }, 5000);
        }
    }
    
    isValidUrl(string) {
        try {
            const url = new URL(string);
            return url.protocol === 'http:' || url.protocol === 'https:';
        } catch (_) {
            return false;
        }
    }
}

// 當頁面載入完成時初始化設定管理器
document.addEventListener('DOMContentLoaded', () => {
    new SettingsManager();
});
