# SpeechPilot Vite+React 遷移說明

## 遷移概述

成功將 SpeechPilot 從傳統的 HTML+CSS+JS 架構遷移到現代的 Vite+React 架構。

## 主要變更

### 1. 新增依賴套件

#### 開發依賴
```json
{
  "vite": "^7.0.5",
  "@vitejs/plugin-react": "^4.3.4",
  "concurrently": "^9.1.0",
  "wait-on": "^8.0.1",
  "cross-env": "^7.0.3"
}
```

#### 生產依賴
```json
{
  "react": "^18.3.1",
  "react-dom": "^18.3.1"
}
```

### 2. 新增配置檔案

#### vite.config.js
- 配置 React 插件
- 設定多入口點 (settings, recording-status)
- 配置輸出目錄為 `dist-renderer`
- 設定開發服務器端口為 3000

### 3. 重構的組件

#### Settings 組件
- **位置**: `src/renderer/components/Settings.jsx`
- **功能**: 完整的設定管理功能
- **特色**: 
  - React Hooks (useState, useEffect)
  - 表單驗證
  - 狀態管理
  - IPC 通信

#### RecordingStatus 組件
- **位置**: `src/renderer/components/RecordingStatus.jsx`
- **功能**: 錄音狀態顯示
- **特色**:
  - 動態狀態切換
  - 動畫效果
  - 錯誤處理

### 4. 新的目錄結構

```
src/renderer/
├── components/
│   ├── Settings.jsx
│   ├── Settings.css
│   ├── RecordingStatus.jsx
│   └── RecordingStatus.css
├── settings.html (簡化版)
├── settings.jsx (入口檔案)
├── recording-status.html (簡化版)
└── recording-status.jsx (入口檔案)
```

### 5. 更新的腳本

#### 開發模式
```bash
npm run dev          # 同時啟動 Vite 和 Electron
npm run dev:vite     # 只啟動 Vite 開發服務器
npm run dev:electron # 只啟動 Electron (需要 Vite 先運行)
```

#### 生產模式
```bash
npm run build:renderer # 建置 React 組件
npm run build         # 建置整個應用程式
npm start             # 啟動生產版本
```

### 6. Electron 整合

#### main.js 修改
- 新增開發/生產模式判斷
- 開發模式載入 Vite 開發服務器
- 生產模式載入建置後的檔案
- 開發模式自動開啟 DevTools

```javascript
const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');

if (isDev) {
  this.mainWindow.loadURL(`${VITE_DEV_SERVER_URL}/src/renderer/settings.html`);
  this.mainWindow.webContents.openDevTools();
} else {
  this.mainWindow.loadFile(path.join(__dirname, '../dist-renderer/settings.html'));
}
```

## 技術優勢

### 1. 開發體驗
- **熱重載**: Vite 提供極快的熱重載
- **現代工具**: 支援 ES6+、JSX、CSS 模組
- **開發工具**: React DevTools 支援
- **TypeScript**: 可輕鬆添加 TypeScript 支援

### 2. 程式碼品質
- **組件化**: React 組件提供更好的程式碼組織
- **狀態管理**: 使用 React Hooks 管理狀態
- **可重用性**: 組件可以輕鬆重用和測試

### 3. 建置效能
- **快速建置**: Vite 使用 esbuild 提供極快的建置速度
- **樹搖**: 自動移除未使用的程式碼
- **程式碼分割**: 支援動態導入和程式碼分割

### 4. 維護性
- **模組化**: 清晰的檔案結構和組件分離
- **CSS 作用域**: 組件級別的 CSS 管理
- **錯誤邊界**: React 錯誤處理機制

## 使用方式

### 開發模式
1. 啟動開發環境：
```bash
npm run dev
```

2. Vite 會在 `http://localhost:3000` 啟動開發服務器
3. Electron 會自動等待 Vite 啟動後載入頁面
4. 修改 React 組件會自動熱重載

### 生產建置
1. 建置 React 組件：
```bash
npm run build:renderer
```

2. 建置完整應用程式：
```bash
npm run build
```

3. 啟動生產版本：
```bash
npm start
```

## 注意事項

### 1. 檔案路徑
- 開發模式使用 `http://localhost:3000/src/renderer/...`
- 生產模式使用 `../dist-renderer/...`

### 2. IPC 通信
- React 組件中使用 `window.require('electron')` 存取 IPC
- 保持與原有 IPC 處理器的兼容性

### 3. CSS 樣式
- 每個組件都有對應的 CSS 檔案
- 保持原有的樣式設計和動畫效果

### 4. 建置輸出
- React 組件建置到 `dist-renderer/` 目錄
- Electron 主程序仍在 `src/` 目錄

## 後續改進建議

1. **TypeScript**: 添加 TypeScript 支援提高程式碼品質
2. **測試**: 添加 React Testing Library 進行組件測試
3. **狀態管理**: 如果應用程式變得更複雜，可考慮使用 Redux 或 Zustand
4. **樣式**: 考慮使用 CSS-in-JS 或 Tailwind CSS
5. **國際化**: 添加 i18n 支援多語言

## 相容性

- ✅ 保持所有原有功能
- ✅ 相同的 IPC 通信機制
- ✅ 相同的設定儲存方式
- ✅ 相同的使用者體驗
- ✅ 支援 .env 環境變數
