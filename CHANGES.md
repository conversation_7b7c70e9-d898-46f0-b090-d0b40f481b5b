# VoicePilot 更新說明

## 修改摘要

本次更新解決了以下問題並新增了功能：

### 1. 修復 UI 視窗大小調整問題 ✅
- **檔案**: `src/main.js` (第 170-183 行)
- **修改**: 將 `resizable` 設為 `true`
- **新增**: 設定最小尺寸 (500x400) 和最大尺寸 (1000x800)
- **結果**: 用戶現在可以調整設定視窗的大小

### 2. 更新 Azure API 版本 ✅
- **檔案**: `src/services/AIService.js`
- **修改**: 將所有 API 版本從 `2024-02-01` 更新為 `2025-01-01-preview`
- **影響**: 支援新的 audio completions API

### 3. 更新 AI 模型選項為手動輸入 ✅
- **檔案**: 
  - `src/renderer/settings.html` (第 162-167 行)
  - `src/renderer/settings.js` (第 28 行)
  - `src/main.js` (第 202 行)
- **修改**: 將下拉選單改為文字輸入框
- **預設值**: `gpt-4o-mini-audio-preview`
- **結果**: 用戶可以手動輸入任何模型名稱

### 4. 創建錄音狀態彈窗 ✅
- **新檔案**: 
  - `src/renderer/recording-status.html` - 美觀的錄音狀態 UI
  - `src/renderer/recording-status.js` - 狀態管理邏輯
- **功能**:
  - 錄音中狀態：顯示脈衝動畫和音波效果
  - 處理中狀態：顯示旋轉動畫
  - 錯誤狀態：顯示錯誤訊息並自動關閉
- **整合**: 在 `src/main.js` 中新增 `showRecordingWindow()` 和 `updateRecordingStatus()` 方法

### 5. 重構 AI 服務使用 audio completions API ✅
- **檔案**: `src/services/AIService.js`
- **重大變更**:
  - 完全重寫 `processAudio()` 方法
  - 移除舊的 `speechToText()` 和 `processCommand()` 方法
  - 直接使用 chat completions API 處理音訊輸入
- **新功能**:
  - 支援 `input_audio` 類型的訊息
  - 使用 base64 編碼的音訊資料
  - 更好的錯誤處理和狀態碼檢查

## 技術細節

### API 變更
```javascript
// 舊的方式 (兩步驟)
1. 音訊 → Whisper API → 文字
2. 文字 → Chat Completions API → 回應

// 新的方式 (一步驟)
音訊 → Chat Completions API (with audio input) → 回應
```

### 新的 API 請求格式
```javascript
{
  model: "gpt-4o-mini-audio-preview",
  modalities: ["text"],
  messages: [
    {
      role: "user",
      content: [
        {
          type: "input_audio",
          input_audio: {
            data: base64Audio,
            format: "wav"
          }
        }
      ]
    }
  ]
}
```

### 錄音狀態視窗特性
- **無邊框**: 透明背景，現代化設計
- **置頂顯示**: 確保用戶能看到錄音狀態
- **動畫效果**: 脈衝、音波、旋轉等視覺回饋
- **自動關閉**: 完成或錯誤時自動關閉

## 使用說明

1. **設定**: 開啟設定視窗，輸入 Azure OpenAI 端點和 API 金鑰
2. **模型**: 在模型欄位輸入 `gpt-4o-mini-audio-preview`
3. **錄音**: 按下快捷鍵 (預設 Ctrl+Shift+V) 開始錄音
4. **狀態**: 錄音時會顯示美觀的狀態視窗
5. **停止**: 再次按下快捷鍵停止錄音並處理

## 注意事項

- 需要 Azure OpenAI 資源支援 `gpt-4o-mini-audio-preview` 模型
- API 版本必須是 `2025-01-01-preview` 或更新
- 音訊檔案大小限制為 20MB
- 支援的音訊格式：WAV

## 測試建議

1. 測試視窗調整功能
2. 測試 API 連接 (使用測試按鈕)
3. 測試錄音狀態視窗顯示
4. 測試語音輸入和文字輸出功能
5. 測試錯誤處理 (無效 API 金鑰等)
