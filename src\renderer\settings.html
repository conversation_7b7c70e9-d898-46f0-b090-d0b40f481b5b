<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpeechPilot Settings</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        input[type="text"], input[type="password"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus, input[type="password"]:focus, select:focus {
            outline: none;
            border-color: #007acc;
            box-shadow: 0 0 0 2px rgba(0,122,204,0.2);
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }
        
        button {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background-color: #007acc;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #005a9e;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .btn-test {
            background-color: #28a745;
            color: white;
        }
        
        .btn-test:hover {
            background-color: #1e7e34;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-size: 14px;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SpeechPilot 設定</h1>
        
        <form id="settingsForm">
            <div class="form-group">
                <label for="hotkey">全域快捷鍵:</label>
                <select id="hotkey" name="hotkey">
                    <option value="CommandOrControl+Shift+V">Ctrl+Shift+V</option>
                    <option value="CommandOrControl+Shift+A">Ctrl+Shift+A</option>
                    <option value="CommandOrControl+Alt+V">Ctrl+Alt+V</option>
                    <option value="F12">F12</option>
                </select>
                <div class="help-text">選擇啟動語音錄製的快捷鍵</div>
            </div>
            
            <div class="form-group">
                <label for="azureEndpoint">Azure OpenAI Endpoint:</label>
                <input type="text" id="azureEndpoint" name="azureEndpoint" 
                       placeholder="https://your-resource.openai.azure.com">
                <div class="help-text">你的Azure OpenAI資源端點URL</div>
            </div>
            
            <div class="form-group">
                <label for="azureApiKey">Azure API Key:</label>
                <input type="password" id="azureApiKey" name="azureApiKey" 
                       placeholder="輸入你的API金鑰">
                <div class="help-text">Azure OpenAI的API金鑰</div>
            </div>
            
            <div class="form-group">
                <label for="model">AI模型:</label>
                <input type="text" id="model" name="model"
                       placeholder="gpt-4o-mini-audio-preview">
                <div class="help-text">輸入要使用的AI模型名稱（建議使用 gpt-4o-mini-audio-preview）</div>
            </div>
            
            <div class="button-group">
                <button type="button" id="testBtn" class="btn-test">測試連接</button>
                <button type="submit" class="btn-primary">儲存設定</button>
                <button type="button" id="cancelBtn" class="btn-secondary">取消</button>
            </div>
        </form>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script src="settings.js"></script>
</body>
</html>
