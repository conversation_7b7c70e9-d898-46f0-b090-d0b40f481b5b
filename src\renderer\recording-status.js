const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class RecordingStatusManager {
    constructor() {
        this.recordingState = document.getElementById('recordingState');
        this.processingState = document.getElementById('processingState');
        this.errorState = document.getElementById('errorState');
        this.errorMessage = document.getElementById('errorMessage');
        
        this.init();
    }
    
    init() {
        // 監聽來自主程序的狀態更新
        ipcRenderer.on('recording-status-update', (event, status) => {
            this.updateStatus(status);
        });
        
        // 初始狀態為錄音中
        this.showRecording();
    }
    
    updateStatus(status) {
        switch (status.type) {
            case 'recording':
                this.showRecording();
                break;
            case 'processing':
                this.showProcessing();
                break;
            case 'error':
                this.showError(status.message);
                break;
            case 'complete':
                this.closeWindow();
                break;
        }
    }
    
    showRecording() {
        this.hideAllStates();
        this.recordingState.style.display = 'block';
    }
    
    showProcessing() {
        this.hideAllStates();
        this.processingState.style.display = 'block';
        this.processingState.classList.add('processing');
    }
    
    showError(message) {
        this.hideAllStates();
        this.errorMessage.textContent = message || '處理失敗';
        this.errorState.style.display = 'block';
        this.errorState.classList.add('error');
        
        // 3秒後自動關閉
        setTimeout(() => {
            this.closeWindow();
        }, 3000);
    }
    
    hideAllStates() {
        this.recordingState.style.display = 'none';
        this.processingState.style.display = 'none';
        this.processingState.classList.remove('processing');
        this.errorState.style.display = 'none';
        this.errorState.classList.remove('error');
    }
    
    closeWindow() {
        window.close();
    }
}

// 當頁面載入完成時初始化
document.addEventListener('DOMContentLoaded', () => {
    new RecordingStatusManager();
});

// 處理ESC鍵關閉視窗
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        window.close();
    }
});
