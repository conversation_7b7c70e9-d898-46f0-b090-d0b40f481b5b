require('dotenv').config();
const axios = require('axios');
const FormData = require('form-data');
const Store = require('electron-store');

class AIService {
  constructor() {
    this.store = new Store();
  }

  async processAudio(audioBuffer) {
    try {
      const endpoint = this.store.get('azureEndpoint') || process.env.AZURE_OPENAI_ENDPOINT;
      const apiKey = this.store.get('azureApiKey') || process.env.AZURE_OPENAI_API_KEY;
      const model = this.store.get('model') || process.env.AZURE_OPENAI_MODEL || 'gpt-4o-mini-audio-preview';

      if (!endpoint || !apiKey) {
        throw new Error('Azure OpenAI credentials not configured');
      }

      // 將音訊轉換為 base64
      const base64Audio = audioBuffer.toString('base64');

      // 使用新的 chat completions API 直接處理音訊
      const response = await axios.post(
        `${endpoint}/openai/chat/completions?api-version=2025-01-01-preview`,
        {
          model: model,
          modalities: ["text"],
          messages: [
            {
              role: "system",
              content: `你是一個智能語音助手，專門幫助用戶處理文字輸入任務。

用戶會透過語音說出各種指令，你需要理解並執行：

1. 直接文字輸入：如果用戶只是想要輸入文字，直接返回該文字
2. 寫作指令：如"寫一篇100字的文章關於..."、"幫我寫一封郵件"等
3. 格式化指令：如"把這個做成列表"、"整理成表格"等
4. 翻譯指令：如"翻譯成英文"、"翻譯成中文"等
5. 編輯指令：如"修正語法"、"改寫得更正式"等

請根據用戶的語音指令，提供適當的回應。如果是寫作任務，請直接提供完整的內容。
回應應該是可以直接輸入到文字欄位的內容，不要包含額外的說明或格式標記。`
            },
            {
              role: "user",
              content: [
                {
                  type: "input_audio",
                  input_audio: {
                    data: base64Audio,
                    format: "wav"
                  }
                }
              ]
            }
          ],
          max_tokens: 1000,
          temperature: 0.7
        },
        {
          headers: {
            'api-key': apiKey,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const aiResponse = response.data.choices[0]?.message?.content || '';

      if (!aiResponse) {
        throw new Error('AI沒有返回有效回應');
      }

      return aiResponse.trim();

    } catch (error) {
      console.error('AI processing error:', error.response?.data || error.message);

      if (error.response?.status === 429) {
        throw new Error('API請求過於頻繁，請稍後再試');
      } else if (error.response?.status === 401) {
        throw new Error('API金鑰無效，請檢查設定');
      } else if (error.response?.status === 400) {
        throw new Error('請求格式錯誤，請檢查模型設定');
      } else {
        throw new Error(`AI處理失敗: ${error.response?.data?.error?.message || error.message}`);
      }
    }
  }





  // 測試API連接
  async testConnection() {
    try {
      const endpoint = this.store.get('azureEndpoint') || process.env.AZURE_OPENAI_ENDPOINT;
      const apiKey = this.store.get('azureApiKey') || process.env.AZURE_OPENAI_API_KEY;

      if (!endpoint || !apiKey) {
        return { success: false, error: 'Missing credentials' };
      }

      const model = this.store.get('model') || process.env.AZURE_OPENAI_MODEL || 'gpt-4o-mini-audio-preview';

      const response = await axios.post(
        `${endpoint}/openai/chat/completions?api-version=2025-01-01-preview`,
        {
          model: model,
          messages: [{ role: 'user', content: 'Hello' }],
          max_tokens: 10
        },
        {
          headers: {
            'api-key': apiKey,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      return { success: true };

    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error?.message || error.message 
      };
    }
  }
}

module.exports = AIService;
