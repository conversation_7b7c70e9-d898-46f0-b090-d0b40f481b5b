const axios = require('axios');
const FormData = require('form-data');
const Store = require('electron-store');

class AIService {
  constructor() {
    this.store = new Store();
  }

  async processAudio(audioBuffer) {
    try {
      // 首先進行語音轉文字
      const transcription = await this.speechToText(audioBuffer);
      console.log('Transcription:', transcription);

      // 然後讓AI處理指令
      const aiResponse = await this.processCommand(transcription);
      console.log('AI Response:', aiResponse);

      return aiResponse;

    } catch (error) {
      console.error('Error processing audio:', error);
      throw error;
    }
  }

  async speechToText(audioBuffer) {
    try {
      const endpoint = this.store.get('azureEndpoint');
      const apiKey = this.store.get('azureApiKey');

      if (!endpoint || !apiKey) {
        throw new Error('Azure OpenAI credentials not configured');
      }

      // 建立FormData用於上傳音訊檔案
      const formData = new FormData();
      formData.append('file', audioBuffer, {
        filename: 'audio.wav',
        contentType: 'audio/wav'
      });
      formData.append('model', 'whisper-1');
      formData.append('language', 'zh'); // 支援中文

      const response = await axios.post(
        `${endpoint}/openai/audio/transcriptions?api-version=2024-02-01`,
        formData,
        {
          headers: {
            'api-key': apiKey,
            ...formData.getHeaders()
          },
          timeout: 30000 // 30秒超時
        }
      );

      return response.data.text || '';

    } catch (error) {
      console.error('Speech to text error:', error.response?.data || error.message);
      throw new Error(`語音轉文字失敗: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async processCommand(text) {
    try {
      const endpoint = this.store.get('azureEndpoint');
      const apiKey = this.store.get('azureApiKey');
      const model = this.store.get('model', 'gpt-4o-mini');

      if (!endpoint || !apiKey) {
        throw new Error('Azure OpenAI credentials not configured');
      }

      // 建立系統提示，讓AI理解各種指令
      const systemPrompt = `你是一個智能語音助手，專門幫助用戶處理文字輸入任務。

用戶會說出各種指令，你需要理解並執行：

1. 直接文字輸入：如果用戶只是想要輸入文字，直接返回該文字
2. 寫作指令：如"寫一篇100字的文章關於..."、"幫我寫一封郵件"等
3. 格式化指令：如"把這個做成列表"、"整理成表格"等
4. 翻譯指令：如"翻譯成英文"、"翻譯成中文"等
5. 編輯指令：如"修正語法"、"改寫得更正式"等

請根據用戶的指令，提供適當的回應。如果是寫作任務，請直接提供完整的內容。
回應應該是可以直接輸入到文字欄位的內容，不要包含額外的說明或格式標記。

用戶輸入：${text}`;

      const response = await axios.post(
        `${endpoint}/openai/chat/completions?api-version=2024-02-01`,
        {
          model: model,
          messages: [
            {
              role: 'system',
              content: systemPrompt
            },
            {
              role: 'user',
              content: text
            }
          ],
          max_tokens: 1000,
          temperature: 0.7
        },
        {
          headers: {
            'api-key': apiKey,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const aiResponse = response.data.choices[0]?.message?.content || '';
      
      if (!aiResponse) {
        throw new Error('AI沒有返回有效回應');
      }

      return aiResponse.trim();

    } catch (error) {
      console.error('AI processing error:', error.response?.data || error.message);
      
      // 如果AI處理失敗，至少返回原始轉錄文字
      if (error.response?.status === 429) {
        throw new Error('API請求過於頻繁，請稍後再試');
      } else if (error.response?.status === 401) {
        throw new Error('API金鑰無效，請檢查設定');
      } else {
        console.log('AI處理失敗，返回原始文字:', text);
        return text; // 降級處理：返回原始轉錄文字
      }
    }
  }

  // 測試API連接
  async testConnection() {
    try {
      const endpoint = this.store.get('azureEndpoint');
      const apiKey = this.store.get('azureApiKey');

      if (!endpoint || !apiKey) {
        return { success: false, error: 'Missing credentials' };
      }

      const response = await axios.post(
        `${endpoint}/openai/chat/completions?api-version=2024-02-01`,
        {
          model: 'gpt-4o-mini',
          messages: [{ role: 'user', content: 'Hello' }],
          max_tokens: 10
        },
        {
          headers: {
            'api-key': apiKey,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      return { success: true };

    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error?.message || error.message 
      };
    }
  }
}

module.exports = AIService;
